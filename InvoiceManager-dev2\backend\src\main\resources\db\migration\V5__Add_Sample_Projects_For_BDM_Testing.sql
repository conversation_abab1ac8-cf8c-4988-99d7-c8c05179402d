-- Add sample projects to demonstrate BDM dependency constraints
-- This will help test the BDM deletion functionality

-- First, ensure we have some clients
INSERT INTO clients (name, created_at, modified_at) 
VALUES 
    ('Test Client 1', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('Test Client 2', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT DO NOTHING;

-- Add sample HSN codes if they don't exist
INSERT INTO hsn_codes (code, description, gst_rate, created_at, modified_at) 
VALUES 
    ('998313', 'IT consulting services', 18.00, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('998314', 'Software development', 18.00, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (code) DO NOTHING;

-- Add sample projects that reference BDMs (this will create dependencies)
INSERT INTO projects (client_id, name, hsn_code_id, description, bdm_id, commission_percentage, created_at, modified_at)
SELECT 
    c.id as client_id,
    'Test Project for ' || b.name as name,
    h.id as hsn_code_id,
    'Sample project to test BDM dependencies' as description,
    b.id as bdm_id,
    5.00 as commission_percentage,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as modified_at
FROM bdms b
CROSS JOIN clients c
CROSS JOIN hsn_codes h
WHERE b.name IN ('Sarah Johnson', '<PERSON>')
  AND c.name = 'Test Client 1'
  AND h.code = '998313'
LIMIT 2;

-- Add one more project for Michael Brown to demonstrate multiple dependencies
INSERT INTO projects (client_id, name, hsn_code_id, description, bdm_id, commission_percentage, created_at, modified_at)
SELECT 
    c.id as client_id,
    'Additional Project for Michael Brown' as name,
    h.id as hsn_code_id,
    'Second project to test multiple dependencies' as description,
    b.id as bdm_id,
    4.50 as commission_percentage,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as modified_at
FROM bdms b
CROSS JOIN clients c
CROSS JOIN hsn_codes h
WHERE b.name = 'Michael Brown'
  AND c.name = 'Test Client 2'
  AND h.code = '998314'
LIMIT 1;
