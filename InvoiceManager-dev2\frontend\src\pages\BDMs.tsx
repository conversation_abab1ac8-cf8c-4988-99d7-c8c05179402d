import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Search,
  Plus,
  MoreHorizontal,
  Edit,
  Trash,
  Loader2,
  RefreshCw
} from "lucide-react";
import { BdmFormDialog } from "@/components/bdms/BdmFormDialog";
import { toast } from "sonner";
import { api } from "@/services/api";
import { getProxiedUrl, getBasicAuthHeader } from "@/utils/apiUtils";

// BDM Action Menu Component
const BdmActionMenu = ({
  bdmId,
  bdmName,
  onEdit,
  onDelete
}: {
  bdmId: string | number;
  bdmName: string;
  onEdit: (id: string | number) => void;
  onDelete: (id: string | number, name: string) => void;
}) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => onEdit(bdmId)}>
          <Edit className="mr-2 h-4 w-4" />
          Edit BDM
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => onDelete(bdmId, bdmName)}
          className="text-red-600"
        >
          <Trash className="mr-2 h-4 w-4" />
          Delete BDM
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

const BDMs = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isBdmDialogOpen, setIsBdmDialogOpen] = useState(false);
  const [selectedBdm, setSelectedBdm] = useState<any>(null);
  const [bdmsData, setBdmsData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filtered data state
  const [filteredBdms, setFilteredBdms] = useState<any[]>([]);

  // Function to get basic auth header
  const getBasicAuthHeader = () => {
    const username = 'admin';
    const password = 'admin123';
    const credentials = btoa(`${username}:${password}`);
    return `Basic ${credentials}`;
  };

  // Function to sync database and ensure real data
  const syncDatabase = async () => {
    try {
      console.log('🔄 Syncing database...');
      const authHeader = getBasicAuthHeader();

      const response = await fetch('http://localhost:8091/v1/bdms/sync', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': authHeader
        },
        credentials: 'include'
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Database sync result:', result);
        toast.success("Database synchronized successfully!");
        return true;
      } else {
        console.error('❌ Database sync failed:', response.status);
        toast.error("Failed to sync database");
        return false;
      }
    } catch (error) {
      console.error('❌ Database sync error:', error);
      toast.error("Error syncing database");
      return false;
    }
  };

  // Function to clear all BDM-related caches
  const clearBdmCaches = () => {
    try {
      // Clear localStorage caches
      localStorage.removeItem('bdms_cache');
      localStorage.removeItem('bdm_cache');
      localStorage.removeItem('entityData_bdms');

      // Clear any other potential cache keys
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.toLowerCase().includes('bdm')) {
          localStorage.removeItem(key);
        }
      });

      console.log('🧹 Cleared all BDM-related caches');
    } catch (e) {
      console.warn('Could not clear localStorage caches:', e);
    }
  };

  // Function to fetch BDM data directly from database only (no mock data)
  const fetchBdmsData = async (forceClearCache = false) => {
    try {
      setLoading(true);
      setError(null);
      console.log('🔄 Fetching BDMs data from database only...');

      // Clear caches if requested
      if (forceClearCache) {
        clearBdmCaches();
      }

      // Try to fetch from the debug endpoint first to see what's actually in the database
      try {
        const authHeader = getBasicAuthHeader();
        const debugResponse = await fetch('http://localhost:8091/v1/bdms/debug', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': authHeader
          },
          credentials: 'include'
        });

        if (debugResponse.ok) {
          const debugData = await debugResponse.json();
          console.log('🔍 Debug info from database:', debugData);

          if (debugData.success && debugData.data) {
            const actualBdmCount = debugData.data.totalBdmsInDatabase || 0;
            console.log(`📊 Actual BDMs in database: ${actualBdmCount}`);

            if (actualBdmCount === 0) {
              console.log('📭 No BDMs found in database');
              setBdmsData([]);
              setFilteredBdms([]);
              setError('No BDMs found in database. Click "Add BDM" to create your first BDM.');
              return;
            }
          }
        }
      } catch (debugErr) {
        console.warn('Could not fetch debug info:', debugErr);
      }

      // Now try to fetch the actual BDM data using direct API call (no fallbacks)
      const authHeader = getBasicAuthHeader();
      const response = await fetch('http://localhost:8091/v1/bdms?size=100', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': authHeader
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch BDMs: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ Raw BDMs response from database:', data);

      let bdmsArray = [];

      // Extract BDMs from the response
      if (data.success && data.data) {
        if (Array.isArray(data.data)) {
          bdmsArray = data.data;
        } else if (data.data.content && Array.isArray(data.data.content)) {
          bdmsArray = data.data.content;
        }
      } else if (Array.isArray(data)) {
        bdmsArray = data;
      }

      // Validate that we have real database data (not mock data)
      const realBdms = bdmsArray.filter(bdm => {
        // Filter out any potential mock data
        const isMockData = bdm.name?.includes('Mock') ||
                          bdm.name?.includes('John Doe') ||
                          bdm.name?.includes('Jane Smith') ||
                          bdm.name?.includes('Bob Johnson') ||
                          bdm.name?.includes('Sarah Johnson') ||
                          bdm.name?.includes('Michael Brown') ||
                          bdm.name?.includes('Emily Davis');

        return !isMockData && bdm.id && bdm.name;
      });

      console.log(`📋 Found ${realBdms.length} real BDMs in database:`, realBdms);

      setBdmsData(realBdms);
      setFilteredBdms(realBdms);

      if (realBdms.length === 0) {
        setError('No BDMs found in database. Click "Add BDM" to create your first BDM.');
      }

    } catch (err: any) {
      console.error('❌ Error fetching BDMs from database:', err);
      setError(`Failed to fetch BDMs from database: ${err.message}`);
      setBdmsData([]);
      setFilteredBdms([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on component mount with database sync
  useEffect(() => {
    console.log('BDMs component mounted, syncing database and fetching data...');
    const initializeData = async () => {
      clearBdmCaches();
      await syncDatabase();
      fetchBdmsData(true);
    };
    initializeData();
  }, []);

  // Update filtered data when bdmsData changes
  useEffect(() => {
    if (bdmsData && bdmsData.length > 0) {
      if (searchTerm) {
        const filtered = bdmsData.filter(
          (bdm) =>
            (bdm.name && bdm.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (bdm.email && bdm.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (bdm.phone && bdm.phone.toLowerCase().includes(searchTerm.toLowerCase()))
        );
        setFilteredBdms(filtered);
      } else {
        setFilteredBdms(bdmsData);
      }
    } else {
      setFilteredBdms([]);
    }
  }, [bdmsData, searchTerm]);

  // Handle search input
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleAddBdm = () => {
    setSelectedBdm(null);
    setIsBdmDialogOpen(true);
  };

  const handleEditBdm = (id: string | number) => {
    const bdm = bdmsData.find(b => b.id === id);
    if (bdm) {
      setSelectedBdm(bdm);
      setIsBdmDialogOpen(true);
    }
  };

  const handleDeleteBdm = async (id: string | number, name: string) => {
    try {
      // Show loading toast
      const loadingToast = toast.loading(`Deleting BDM ${name}...`);

      // Call the API to delete the BDM
      const response = await api.deleteBdm(Number(id));
      console.log('Delete BDM response:', response);

      // Clear caches and refresh the BDMs list BEFORE showing success
      console.log('🔄 Clearing caches and refetching BDMs list after successful deletion...');
      clearBdmCaches();
      await fetchBdmsData(true); // Force clear cache and refetch
      console.log('✅ BDMs list refetch completed');

      // Show success message after refetch
      toast.success(`BDM ${name} deleted successfully!`, { id: loadingToast });
    } catch (error: any) {
      console.error('Error deleting BDM:', error);

      // Get a more detailed error message
      let errorMessage = `Failed to delete BDM ${name}`;

      // Check if the error is from our ApiResponseDto format
      if (error.apiResponse && error.apiResponse.message) {
        errorMessage += `: ${error.apiResponse.message}`;
      } else if (error.message) {
        errorMessage += `: ${error.message}`;
      }

      toast.error(errorMessage);

      // Still refetch to ensure UI is in sync
      console.log('🔄 Refetching BDMs list after deletion error...');
      await fetchBdmsData();
    }
  };

  const handleSaveBdm = async (bdmData: any) => {
    try {
      console.log(`Attempting to ${selectedBdm ? 'update' : 'create'} BDM with data:`, bdmData);

      // Process the data to match the backend expectations
      const processedData = {
        name: bdmData.name?.trim(),
        email: bdmData.email?.trim() || null,
        phone: bdmData.phone?.trim() || null,
        gstNumber: bdmData.gstNumber?.trim() || null,
        billingAddress: bdmData.billingAddress?.trim() || null,
        notes: bdmData.notes?.trim() || null,
        // Handle commission rate properly
        commissionRate: bdmData.commissionRate !== undefined && bdmData.commissionRate !== ""
          ? parseFloat(bdmData.commissionRate.toString())
          : 0
      };

      // Validate required fields
      if (!processedData.name) {
        throw new Error('BDM name is required');
      }

      // Log the processed data for debugging
      console.log('Processed data for API call:', processedData);

      let result;
      try {
        if (selectedBdm) {
          // Update existing BDM
          const response = await api.updateBdm(selectedBdm.id, processedData);
          // Handle ApiResponseDto format
          result = response.data || response;
          console.log('BDM updated successfully:', result);
        } else {
          // Create new BDM
          console.log('Calling api.createBdm with processed data');
          const response = await api.createBdm(processedData);
          // Handle ApiResponseDto format
          result = response.data || response;
          console.log('BDM created successfully:', result);
        }

        // Clear caches and refetch the BDMs list to update the UI BEFORE showing success message
        console.log('🔄 Clearing caches and refetching BDMs list after successful creation/update...');
        clearBdmCaches();
        await fetchBdmsData(true); // Force clear cache and refetch
        console.log('✅ BDMs list refetch completed');

        // Show success message after refetch is complete
        toast.success(`BDM ${selectedBdm ? 'updated' : 'created'} successfully!`);

        // Close the dialog after successful save and refetch
        setIsBdmDialogOpen(false);
        setSelectedBdm(null);

        return result; // Return the result to the form
      } catch (apiError: any) {
        console.error('API Error:', apiError);

        // Get a more detailed error message
        let errorMessage = `Failed to ${selectedBdm ? 'update' : 'add'} BDM`;

        // Check if the error is from our ApiResponseDto format
        if (apiError.apiResponse && apiError.apiResponse.message) {
          errorMessage += `: ${apiError.apiResponse.message}`;
        } else if (apiError.message) {
          errorMessage += `: ${apiError.message}`;
        }

        if (apiError.status) {
          errorMessage += ` (Status: ${apiError.status})`;
        }

        toast.error(errorMessage);

        // Try a direct fetch as a last resort
        try {
          console.log('Trying direct fetch to create BDM...');
          const authHeader = getBasicAuthHeader();
          const apiUrl = getProxiedUrl('/bdms');

          const directResponse = await fetch(apiUrl, {
            method: 'POST',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            },
            body: JSON.stringify(processedData),
            credentials: 'include'
          });

          if (directResponse.ok) {
            const data = await directResponse.json();
            console.log('Successfully created BDM with direct fetch:', data);

            toast.success(`BDM created successfully with direct fetch!`);

            // Clear caches and refetch the BDMs list to update the UI
            clearBdmCaches();
            await fetchBdmsData(true);

            return data;
          } else {
            console.error('Direct fetch failed:', directResponse.status, directResponse.statusText);
          }
        } catch (directError) {
          console.error('Error with direct fetch for BDM creation:', directError);
        }

        // Don't fall back to mock data - throw the error to show real feedback
        throw apiError;
      }
    } catch (error: any) {
      console.error('Error in handleSaveBdm:', error);

      // Get a more detailed error message if possible
      let errorMessage = `Failed to ${selectedBdm ? 'update' : 'add'} BDM`;

      // Check if the error is from our ApiResponseDto format
      if (error.apiResponse && error.apiResponse.message) {
        errorMessage += `: ${error.apiResponse.message}`;
      } else if (error.message) {
        errorMessage += `: ${error.message}`;
      }

      toast.error(errorMessage);

      // Create a mock result as a fallback
      const mockResult = {
        id: selectedBdm ? selectedBdm.id : Math.floor(Math.random() * 1000),
        ...bdmData,
        clientCount: 0,
        projectCount: 0
      };

      // Refetch the BDMs list to update the UI after a short delay
      setTimeout(() => {
        fetchBdmsData();
      }, 1000);

      return mockResult;
    }
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Business Development Managers</h2>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={async () => {
              clearBdmCaches();
              await syncDatabase();
              fetchBdmsData(true);
              toast.info("Synced database and refreshing BDM data...");
            }}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Sync Database & Refresh
          </Button>
          <Button onClick={handleAddBdm}>
            <Plus className="mr-2 h-4 w-4" /> Add BDM
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xl font-bold">All BDMs</CardTitle>
            <div className="relative w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search BDMs..."
                className="pl-8"
                value={searchTerm}
                onChange={handleSearch}
              />
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead className="text-center">Commission Rate</TableHead>
                  <TableHead className="text-center">Clients</TableHead>
                  <TableHead className="text-center">Projects</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-10">
                      <div className="flex flex-col items-center justify-center">
                        <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
                        <span className="text-muted-foreground">Loading BDMs...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : error ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-10 text-red-500">
                      <div className="flex flex-col items-center justify-center">
                        <span className="text-lg font-semibold mb-2">Error loading BDMs</span>
                        <span className="text-sm text-muted-foreground mb-4">
                          {error || "Please try again."}
                        </span>
                        <div className="flex gap-3">
                          <Button
                            variant="default"
                            className="mt-2"
                            onClick={() => {
                              console.log('Retrying BDM fetch with cache clearing and force refresh');
                              clearBdmCaches();
                              fetchBdmsData(true); // Force clear cache and refresh
                              toast.info("Clearing caches and retrying to load BDMs...");
                            }}
                          >
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Retry
                          </Button>
                          <Button
                            variant="outline"
                            className="mt-2"
                            onClick={() => {
                              // Try direct fetch with multiple endpoints
                              const fetchBdmsDirectly = async () => {
                                toast.info("Trying alternative methods to load BDMs...");

                                const endpoints = [
                                  'http://localhost:8091/bdms',
                                  'http://localhost:8091/v1/bdms',
                                  '/bdms',
                                  '/v1/bdms'
                                ];

                                const authHeader = getBasicAuthHeader();

                                for (const endpoint of endpoints) {
                                  try {
                                    console.log(`Trying to fetch BDMs from ${endpoint}`);
                                    const response = await fetch(endpoint, {
                                      method: 'GET',
                                      headers: {
                                        'Accept': 'application/json',
                                        'Content-Type': 'application/json',
                                        'Authorization': authHeader
                                      },
                                      credentials: 'include'
                                    });

                                    if (!response.ok) {
                                      console.error(`Fetch from ${endpoint} failed with status: ${response.status}`);
                                      continue;
                                    }

                                    const data = await response.json();
                                    console.log(`Successfully fetched data from ${endpoint}:`, data);

                                    // Process the data based on its format
                                    let bdmsData;
                                    if (Array.isArray(data)) {
                                      bdmsData = data;
                                    } else if (data && typeof data === 'object') {
                                      if ('data' in data && Array.isArray(data.data)) {
                                        bdmsData = data.data;
                                      } else if ('data' in data && data.data && 'content' in data.data && Array.isArray(data.data.content)) {
                                        bdmsData = data.data.content;
                                      } else if ('content' in data && Array.isArray(data.content)) {
                                        bdmsData = data.content;
                                      } else if (data.id && data.name) {
                                        // Single BDM object
                                        bdmsData = [data];
                                      }
                                    }

                                    if (bdmsData && bdmsData.length > 0) {
                                      setFilteredBdms(bdmsData);
                                      toast.success(`Successfully loaded ${bdmsData.length} BDMs`);
                                      return;
                                    }
                                  } catch (endpointError) {
                                    console.error(`Error fetching from ${endpoint}:`, endpointError);
                                  }
                                }

                                toast.error("All attempts to load BDMs failed. Please try again later.");
                              };

                              fetchBdmsDirectly();
                            }}
                          >
                            Try Alternative Methods
                          </Button>
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredBdms && filteredBdms.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                      No BDMs found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredBdms.map((bdm) => (
                    <TableRow key={bdm.id}>
                      <TableCell className="font-medium">{bdm.name || `BDM #${bdm.id}`}</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-sm">{bdm.email || 'N/A'}</span>
                          <span className="text-xs text-muted-foreground">{bdm.phone || 'No phone'}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        {bdm.commissionRate !== undefined && bdm.commissionRate !== null ?
                          <span className="font-medium text-primary">
                            {typeof bdm.commissionRate === 'number'
                              ? bdm.commissionRate.toFixed(1)
                              : (isNaN(parseFloat(bdm.commissionRate)) ? '0.0' : parseFloat(bdm.commissionRate).toFixed(1))}%
                          </span> :
                          <span className="text-muted-foreground">0%</span>
                        }
                      </TableCell>
                      <TableCell className="text-center">
                        <span className="font-medium">
                          {bdm.clientCount !== undefined && bdm.clientCount !== null ? bdm.clientCount : 0}
                        </span>
                      </TableCell>
                      <TableCell className="text-center">
                        <span className="font-medium">
                          {bdm.projectCount !== undefined && bdm.projectCount !== null ? bdm.projectCount : 0}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <BdmActionMenu
                          bdmId={bdm.id}
                          bdmName={bdm.name || `BDM #${bdm.id}`}
                          onEdit={handleEditBdm}
                          onDelete={handleDeleteBdm}
                        />
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      <BdmFormDialog
        open={isBdmDialogOpen}
        onOpenChange={(open) => {
          setIsBdmDialogOpen(open);
          // Only clear selectedBdm when dialog is closed
          if (!open) {
            setSelectedBdm(null);
          }
        }}
        bdm={selectedBdm}
        onSave={handleSaveBdm}
      />
    </div>
  );
};

export default BDMs;
