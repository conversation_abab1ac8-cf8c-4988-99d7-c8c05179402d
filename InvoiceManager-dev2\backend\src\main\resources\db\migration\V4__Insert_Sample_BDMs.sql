-- Insert sample BDM data to match frontend expectations
-- This ensures that BDM IDs 1, 2, and 3 exist in the database

INSERT INTO bdms (name, email, phone, gst_number, billing_address, commission_rate, notes, created_at, modified_at) 
VALUES 
    ('<PERSON>', '<EMAIL>', '************', '22AAAAA0000A1Z5', '123 Business Ave, Suite 100, New York, NY 10001', 5.00, 'Senior BDM with 5+ years experience', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('<PERSON>', '<EMAIL>', '************', '22BBBBB0000B1Z5', '456 Corporate Blvd, Chicago, IL 60601', 4.50, 'Experienced in enterprise sales', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('<PERSON>', '<EMAIL>', '************', '22CCCCC0000C1Z5', '789 Market St, San Francisco, CA 94103', 6.00, 'Top performer in tech sector', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT DO NOTHING;
