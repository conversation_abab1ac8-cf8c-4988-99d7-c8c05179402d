-- Database setup script for InvoiceManager
-- This script creates the database and user if they don't exist

-- Connect to postgres database first
\c postgres;

-- Create database if it doesn't exist
SELECT 'CREATE DATABASE invoiceapp'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'invoiceapp')\gexec

-- Create user if it doesn't exist (optional, since we're using postgres user)
DO
$do$
BEGIN
   IF NOT EXISTS (
      SELECT FROM pg_catalog.pg_roles
      WHERE  rolname = 'invoiceapp_user') THEN

      CREATE ROLE invoiceapp_user LOGIN PASSWORD 'postgres';
   END IF;
END
$do$;

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE invoiceapp TO postgres;
GRANT ALL PRIVILEGES ON DATABASE invoiceapp TO invoiceapp_user;

-- Connect to the invoiceapp database
\c invoiceapp;

-- Grant schema privileges
GRANT ALL ON SCHEMA public TO postgres;
GRANT ALL ON SCHEMA public TO invoiceapp_user;
GRANT ALL ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL ON ALL TABLES IN SCHEMA public TO invoiceapp_user;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO postgres;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO invoiceapp_user;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO postgres;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO invoiceapp_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO postgres;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO invoiceapp_user;
