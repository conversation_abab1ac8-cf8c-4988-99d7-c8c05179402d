-- Clean up and synchronize BDM data to ensure consistency
-- This migration ensures that the BDM table has the correct structure and sample data

-- First, ensure the BDM table exists with the correct structure
CREATE TABLE IF NOT EXISTS bdms (
    id BIGSERIAL PRIMARY KEY,
    name <PERSON><PERSON>HA<PERSON>(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    gst_number VARCHAR(50),
    billing_address TEXT,
    commission_rate NUMERIC(5,2) DEFAULT 0.00,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Clear any existing inconsistent data
DELETE FROM bdms WHERE name IN ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON> Kadam') AND id NOT IN (
    SELECT id FROM bdms WHERE name IN ('<PERSON>', '<PERSON>', '<PERSON>')
);

-- Insert the standard sample BDMs if they don't exist
INSERT INTO bdms (name, email, phone, gst_number, billing_address, commission_rate, notes, created_at, modified_at) 
SELECT '<PERSON>', '<EMAIL>', '************', '22AAAAA0000A1Z5', '123 Business Ave, Suite 100, New York, NY 10001', 5.00, 'Senior BDM with 5+ years experience', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
WHERE NOT EXISTS (SELECT 1 FROM bdms WHERE name = 'Sarah Johnson');

INSERT INTO bdms (name, email, phone, gst_number, billing_address, commission_rate, notes, created_at, modified_at) 
SELECT 'Michael Brown', '<EMAIL>', '************', '22BBBBB0000B1Z5', '456 Corporate Blvd, Chicago, IL 60601', 4.50, 'Experienced in enterprise sales', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
WHERE NOT EXISTS (SELECT 1 FROM bdms WHERE name = 'Michael Brown');

INSERT INTO bdms (name, email, phone, gst_number, billing_address, commission_rate, notes, created_at, modified_at) 
SELECT 'Emily Davis', '<EMAIL>', '************', '22CCCCC0000C1Z5', '789 Market St, San Francisco, CA 94103', 6.00, 'Top performer in tech sector', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
WHERE NOT EXISTS (SELECT 1 FROM bdms WHERE name = 'Emily Davis');

-- Update the sequence to ensure proper ID generation
SELECT setval('bdms_id_seq', COALESCE((SELECT MAX(id) FROM bdms), 1), true);
