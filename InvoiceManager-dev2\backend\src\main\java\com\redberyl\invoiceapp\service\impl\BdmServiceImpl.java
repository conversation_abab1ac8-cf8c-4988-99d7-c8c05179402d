package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.BdmDto;
import com.redberyl.invoiceapp.entity.Bdm;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.repository.BdmRepository;
import com.redberyl.invoiceapp.service.BdmService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class BdmServiceImpl implements BdmService {

    @Autowired
    private BdmRepository bdmRepository;

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<BdmDto> getAllBdms() {
        List<Bdm> bdms = bdmRepository.findAll();
        if (bdms.isEmpty()) {
            throw new NoContentException("No BDMs found");
        }
        return bdms.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public Page<BdmDto> getBdms(Pageable pageable) {
        Page<Bdm> bdmPage = bdmRepository.findAll(pageable);
        List<BdmDto> bdmDtos = bdmPage.getContent().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());

        return new PageImpl<>(bdmDtos, pageable, bdmPage.getTotalElements());
    }

    @Override
    public Page<BdmDto> getBdmsWithFilters(Map<String, String> filters, Pageable pageable) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Bdm> query = cb.createQuery(Bdm.class);
        Root<Bdm> root = query.from(Bdm.class);

        // Create predicates from filters
        List<Predicate> predicates = new ArrayList<>();

        if (filters != null && !filters.isEmpty()) {
            for (Map.Entry<String, String> entry : filters.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();

                if (value != null && !value.isEmpty()) {
                    switch (key) {
                        case "name":
                            predicates.add(cb.like(cb.lower(root.get("name")), "%" + value.toLowerCase() + "%"));
                            break;
                        case "email":
                            predicates.add(cb.like(cb.lower(root.get("email")), "%" + value.toLowerCase() + "%"));
                            break;
                        case "phone":
                            predicates.add(cb.like(cb.lower(root.get("phone")), "%" + value.toLowerCase() + "%"));
                            break;
                        case "gstNumber":
                            predicates.add(cb.like(cb.lower(root.get("gstNumber")), "%" + value.toLowerCase() + "%"));
                            break;
                        case "minCommissionRate":
                            try {
                                BigDecimal minRate = new BigDecimal(value);
                                predicates.add(cb.greaterThanOrEqualTo(root.get("commissionRate"), minRate));
                            } catch (NumberFormatException e) {
                                // Ignore invalid number format
                            }
                            break;
                        case "maxCommissionRate":
                            try {
                                BigDecimal maxRate = new BigDecimal(value);
                                predicates.add(cb.lessThanOrEqualTo(root.get("commissionRate"), maxRate));
                            } catch (NumberFormatException e) {
                                // Ignore invalid number format
                            }
                            break;
                    }
                }
            }
        }

        // Apply predicates to query
        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // Apply sorting
        if (pageable.getSort().isSorted()) {
            List<jakarta.persistence.criteria.Order> orders = new ArrayList<>();
            pageable.getSort().forEach(order -> {
                if (order.isAscending()) {
                    orders.add(cb.asc(root.get(order.getProperty())));
                } else {
                    orders.add(cb.desc(root.get(order.getProperty())));
                }
            });
            query.orderBy(orders);
        }

        // Execute query with pagination
        TypedQuery<Bdm> typedQuery = entityManager.createQuery(query);
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        List<Bdm> bdms = typedQuery.getResultList();

        // Count total elements
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<Bdm> countRoot = countQuery.from(Bdm.class);
        countQuery.select(cb.count(countRoot));

        if (!predicates.isEmpty()) {
            countQuery.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        Long totalElements = entityManager.createQuery(countQuery).getSingleResult();

        // Convert to DTOs
        List<BdmDto> bdmDtos = bdms.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());

        return new PageImpl<>(bdmDtos, pageable, totalElements);
    }

    @Override
    public BdmDto getBdmById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "BDM ID cannot be null");
        }

        return bdmRepository.findById(id)
                .map(this::convertToDto)
                .orElseThrow(() -> new ResourceNotFoundException("BDM not found with id: " + id));
    }

    @Override
    @Transactional
    public BdmDto createBdm(BdmDto bdmDto) {
        validateBdmDto(bdmDto);

        try {
            Bdm bdm = convertToEntity(bdmDto);
            Bdm savedBdm = bdmRepository.save(bdm);
            return convertToDto(savedBdm);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("name", "BDM name cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("email", "BDM with this email already exists");
            } else {
                throw new CustomException("Error creating BDM: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating BDM", e);
        }
    }

    private void validateBdmDto(BdmDto bdmDto) {
        if (bdmDto == null) {
            throw new NullConstraintViolationException("bdmDto", "BDM data cannot be null");
        }

        if (!StringUtils.hasText(bdmDto.getName())) {
            throw new NullConstraintViolationException("name", "BDM name cannot be empty");
        }

        if (bdmDto.getEmail() != null && !bdmDto.getEmail().matches("^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$")) {
            throw new NullConstraintViolationException("email", "Invalid email format");
        }
    }

    @Override
    @Transactional
    public BdmDto updateBdm(Long id, BdmDto bdmDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "BDM ID cannot be null");
        }

        validateBdmDto(bdmDto);

        Bdm existingBdm = bdmRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("BDM not found with id: " + id));

        try {
            existingBdm.setName(bdmDto.getName());
            existingBdm.setEmail(bdmDto.getEmail());
            existingBdm.setPhone(bdmDto.getPhone());
            existingBdm.setGstNumber(bdmDto.getGstNumber());
            existingBdm.setBillingAddress(bdmDto.getBillingAddress());
            existingBdm.setCommissionRate(bdmDto.getCommissionRate());
            existingBdm.setNotes(bdmDto.getNotes());

            Bdm updatedBdm = bdmRepository.save(existingBdm);
            return convertToDto(updatedBdm);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("name", "BDM name cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("email", "BDM with this email already exists");
            } else {
                throw new CustomException("Error updating BDM: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error updating BDM", e);
        }
    }

    @Override
    @Transactional
    public void deleteBdm(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "BDM ID cannot be null");
        }

        Bdm bdm = bdmRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("BDM not found with id: " + id));

        // Check for dependencies before deletion
        checkBdmDependencies(bdm);

        try {
            bdmRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete BDM because it is referenced by other entities. Please remove all associated projects and payments first.", e);
            } else {
                throw new CustomException("Error deleting BDM: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting BDM", e);
        }
    }

    @Override
    public Map<String, Object> checkBdmDependencies(Long id) {
        Bdm bdm = bdmRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("BDM not found with id: " + id));

        Map<String, Object> result = new HashMap<>();
        List<String> dependencies = new ArrayList<>();

        int projectCount = 0;
        int paymentCount = 0;

        // Check for associated projects
        if (bdm.getProjects() != null && !bdm.getProjects().isEmpty()) {
            projectCount = bdm.getProjects().size();
            dependencies.add(projectCount + " project(s)");
        }

        // Check for associated payments
        if (bdm.getPayments() != null && !bdm.getPayments().isEmpty()) {
            paymentCount = bdm.getPayments().size();
            dependencies.add(paymentCount + " payment(s)");
        }

        result.put("canDelete", dependencies.isEmpty());
        result.put("projectCount", projectCount);
        result.put("paymentCount", paymentCount);
        result.put("dependencies", dependencies);
        result.put("message", dependencies.isEmpty() ?
            "BDM can be safely deleted" :
            "BDM has dependencies: " + String.join(", ", dependencies));

        return result;
    }

    @Override
    public Map<String, Object> getDebugInfo() {
        Map<String, Object> debugInfo = new HashMap<>();

        try {
            // Get all BDMs from database
            List<Bdm> allBdms = bdmRepository.findAll();

            debugInfo.put("totalBdmsInDatabase", allBdms.size());
            debugInfo.put("bdmIds", allBdms.stream().map(Bdm::getId).collect(Collectors.toList()));
            debugInfo.put("bdmNames", allBdms.stream().map(Bdm::getName).collect(Collectors.toList()));

            // Get detailed BDM information
            List<Map<String, Object>> bdmDetails = allBdms.stream().map(bdm -> {
                Map<String, Object> details = new HashMap<>();
                details.put("id", bdm.getId());
                details.put("name", bdm.getName());
                details.put("email", bdm.getEmail());
                details.put("phone", bdm.getPhone());
                details.put("projectCount", bdm.getProjects() != null ? bdm.getProjects().size() : 0);
                details.put("paymentCount", bdm.getPayments() != null ? bdm.getPayments().size() : 0);
                return details;
            }).collect(Collectors.toList());

            debugInfo.put("bdmDetails", bdmDetails);
            debugInfo.put("timestamp", System.currentTimeMillis());

        } catch (Exception e) {
            debugInfo.put("error", "Failed to retrieve debug info: " + e.getMessage());
            debugInfo.put("exception", e.getClass().getSimpleName());
        }

        return debugInfo;
    }

    private void checkBdmDependencies(Bdm bdm) {
        List<String> dependencies = new ArrayList<>();

        // Check for associated projects
        if (bdm.getProjects() != null && !bdm.getProjects().isEmpty()) {
            dependencies.add(bdm.getProjects().size() + " project(s)");
        }

        // Check for associated payments
        if (bdm.getPayments() != null && !bdm.getPayments().isEmpty()) {
            dependencies.add(bdm.getPayments().size() + " payment(s)");
        }

        if (!dependencies.isEmpty()) {
            String dependencyList = String.join(", ", dependencies);
            throw new CustomException(
                String.format("Cannot delete BDM '%s' because it has associated %s. " +
                        "Please remove or reassign these dependencies first.",
                        bdm.getName(), dependencyList)
            );
        }
    }

    private BdmDto convertToDto(Bdm bdm) {
        BdmDto dto = BdmDto.builder()
                .id(bdm.getId())
                .name(bdm.getName())
                .email(bdm.getEmail())
                .phone(bdm.getPhone())
                .gstNumber(bdm.getGstNumber())
                .billingAddress(bdm.getBillingAddress())
                .commissionRate(bdm.getCommissionRate())
                .notes(bdm.getNotes())
                .build();

        // Set client count if available
        if (bdm.getProjects() != null) {
            // Count unique clients
            long clientCount = bdm.getProjects().stream()
                    .filter(p -> p.getClient() != null)
                    .map(p -> p.getClient().getId())
                    .distinct()
                    .count();
            dto.setClientCount((int) clientCount);

            // Set project count
            dto.setProjectCount(bdm.getProjects().size());
        }

        return dto;
    }

    private Bdm convertToEntity(BdmDto bdmDto) {
        // Handle null commission rate
        BigDecimal commissionRate = BigDecimal.ZERO;
        if (bdmDto.getCommissionRate() != null) {
            commissionRate = bdmDto.getCommissionRate();
        }

        return Bdm.builder()
                .id(bdmDto.getId())
                .name(bdmDto.getName())
                .email(bdmDto.getEmail())
                .phone(bdmDto.getPhone())
                .gstNumber(bdmDto.getGstNumber())
                .billingAddress(bdmDto.getBillingAddress())
                .commissionRate(commissionRate)
                .notes(bdmDto.getNotes())
                .build();
    }
}
