package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.BdmDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

public interface BdmService {
    /**
     * Get all BDMs
     * @return List of all BDMs
     */
    List<BdmDto> getAllBdms();

    /**
     * Get BDMs with pagination and sorting
     * @param pageable Pagination and sorting information
     * @return Page of BDMs
     */
    Page<BdmDto> getBdms(Pageable pageable);

    /**
     * Get BDMs with filtering, pagination, and sorting
     * @param filters Map of filter criteria
     * @param pageable Pagination and sorting information
     * @return Page of filtered BDMs
     */
    Page<BdmDto> getBdmsWithFilters(Map<String, String> filters, Pageable pageable);

    /**
     * Get a BDM by ID
     * @param id BDM ID
     * @return BDM with the specified ID
     */
    BdmDto getBdmById(Long id);

    /**
     * Create a new BDM
     * @param bdmDto BDM data
     * @return Created BDM
     */
    BdmDto createBdm(BdmDto bdmDto);

    /**
     * Update an existing BDM
     * @param id BDM ID
     * @param bdmDto Updated BDM data
     * @return Updated BDM
     */
    BdmDto updateBdm(Long id, BdmDto bdmDto);

    /**
     * Delete a BDM
     * @param id BDM ID
     */
    void deleteBdm(Long id);

    /**
     * Check BDM dependencies before deletion
     * @param id BDM ID
     * @return Map containing dependency information
     */
    Map<String, Object> checkBdmDependencies(Long id);

    /**
     * Get debug information about BDMs
     * @return Map containing debug information
     */
    Map<String, Object> getDebugInfo();
}
